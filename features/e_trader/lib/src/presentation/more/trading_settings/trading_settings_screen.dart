import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_account_password/change_account_password_screen.dart';
import 'package:e_trader/src/presentation/change_leverage/change_leverage_screen.dart';
import 'package:e_trader/src/presentation/more/trading_settings/bloc/trading_settings_bloc.dart';
import 'package:e_trader/src/presentation/more/widgets/settings_composable_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TradingSettingsScreen extends StatefulWidget {
  const TradingSettingsScreen();

  @override
  State<TradingSettingsScreen> createState() => _TradingSettingsScreenState();
}

class _TradingSettingsScreenState extends State<TradingSettingsScreen> {
  final String _visibilityKey = 'toggle_visibility_key';
  bool _isToggleVisible = false;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (_) =>
              diContainer<TradingSettingsBloc>()
                ..add(TradingSettingsEvent.fetchPrefrences()),
      child: CustomScrollView(
        slivers: [
          BlocBuilder<TradingSettingsBloc, TradingSettingsState>(
            buildWhen: (previous, current) => previous != current,
            builder: (blocBuilderContext, state) {
              return switch (state) {
                TradingSettingsInitial() => const SliverToBoxAdapter(
                  child: SizedBox.shrink(),
                ),
                //
                TradingSettingsSuccess(
                  :final leverage,
                  :final accountNumber,
                  :final showCloseAllTradesConfirmation,
                  :final platformType,
                  :final accountNickname,
                ) =>
                  () {
                    final l10n = EquitiLocalization.of(blocBuilderContext);
                    SettingScreenSection accountSection = SettingScreenSection(
                      children: [
                        const SizedBox(height: 8),
                        DuploText(
                          text: l10n.trader_account,
                          style: blocBuilderContext.duploTextStyles.textMd,
                          color: blocBuilderContext.duploTheme.text.textPrimary,
                          fontWeight: DuploFontWeight.semiBold,
                        ),
                        const SizedBox(height: 6),
                        DuploTap(
                          onTap: () {
                            DuploSheet.showModalSheetV2<void>(
                              context,
                              appBar: DuploAppBar(
                                title: "",
                                titleWidget: DuploText(
                                  text: l10n.trader_changeLeverage,
                                  style:
                                      blocBuilderContext.duploTextStyles.textLg,
                                  color:
                                      blocBuilderContext
                                          .duploTheme
                                          .text
                                          .textPrimary,
                                  fontWeight: DuploFontWeight.bold,
                                ),
                                automaticallyImplyLeading: false,
                                duploAppBarTextAlign: DuploAppBarTextAlign.left,
                                actions: [
                                  IconButton(
                                    icon:
                                        diContainer<ThemeManager>().isDarkMode
                                            ? Assets.images.closeIc.svg(
                                              colorFilter: ColorFilter.mode(
                                                DuploTheme.of(
                                                  context,
                                                ).foreground.fgSecondary,
                                                BlendMode.srcIn,
                                              ),
                                            )
                                            : Assets.images.closeIc.svg(),
                                    onPressed: () => Navigator.pop(context),
                                  ),
                                ],
                              ),
                              content: ChangeLeverageScreen(
                                accountNumber: accountNumber,
                              ),
                              bottomBar: Container(height: 0),
                            ).then((ccc) {
                              blocBuilderContext
                                  .read<TradingSettingsBloc>()
                                  .add(TradingSettingsEvent.fetchPrefrences());
                            });
                          },
                          child: DuploLabelInfoChevronWidget(
                            title: l10n.trader_changeLeverageChevron,
                            valueText: leverage,
                            addInfoIcon: true,
                          ),
                        ),
                        if (platformType == PlatformType.mt5)
                          DuploTap(
                            onTap: () {
                              DuploSheet.showModalSheetV2<void>(
                                context,
                                appBar: DuploAppBar(
                                  title: "",
                                  titleWidget: Column(
                                    children: [
                                      DuploText(
                                        text:
                                            l10n.trader_changeAccountPasswordTitle,
                                        color:
                                            context.duploTheme.text.textPrimary,
                                        style: context.duploTextStyles.textSm,
                                        fontWeight: DuploFontWeight.semiBold,
                                      ),
                                      const SizedBox(height: 4),
                                      DuploText(
                                        text: accountNickname,
                                        color:
                                            context
                                                .duploTheme
                                                .text
                                                .textSecondary,
                                        style: context.duploTextStyles.textXs,
                                        fontWeight: DuploFontWeight.regular,
                                      ),
                                    ],
                                  ),
                                ),
                                content: ChangeAccountPasswordScreen(
                                  accountNumber: accountNumber,
                                ),
                              );
                            },
                            child: DuploLabelInfoChevronWidget(
                              title: l10n.trader_changeAccountPasswordTitle,
                              addInfoIcon: true,
                            ),
                          ),

                        const SizedBox(height: 12),
                        DuploText(
                          text: l10n.trader_tradingPreferences,
                          style: blocBuilderContext.duploTextStyles.textMd,
                          color: blocBuilderContext.duploTheme.text.textPrimary,
                          fontWeight: DuploFontWeight.semiBold,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          child: VisibilityDetector(
                            key: Key(_visibilityKey),
                            onVisibilityChanged: (info) {
                              if (info.visibleFraction > 0) {
                                if (!_isToggleVisible) {
                                  blocBuilderContext
                                      .read<TradingSettingsBloc>()
                                      .add(
                                        TradingSettingsEvent.fetchToggleStatus(),
                                      );
                                  _isToggleVisible = true;
                                }
                              } else {
                                if (_isToggleVisible) {
                                  _isToggleVisible = false;
                                }
                              }
                            },
                            child: Row(
                              children: [
                                Expanded(
                                  child: DuploText(
                                    text:
                                        l10n.trader_showCloseAllTradesConfirmation,
                                    style: context.duploTextStyles.textSm,
                                    color:
                                        context.duploTheme.text.textSecondary,
                                    fontWeight: DuploFontWeight.medium,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                DuploSwitchControl(
                                  initialValue: showCloseAllTradesConfirmation,
                                  onChanged: (value) {
                                    blocBuilderContext
                                        .read<TradingSettingsBloc>()
                                        .add(
                                          TradingSettingsEvent.toggleCloseAllTradesConfirmation(
                                            value,
                                          ),
                                        );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );

                    return SettingsComposableScreen(
                      sections: [accountSection],
                      title: l10n.trader_tradingPreferences,
                    );
                  }(),
              };
            },
          ),
        ],
      ),
    );
  }
}
