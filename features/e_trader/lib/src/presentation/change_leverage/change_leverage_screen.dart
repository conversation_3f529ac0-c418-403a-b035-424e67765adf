import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_leverage/bloc/change_leverage_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ChangeLeverageScreen extends StatelessWidget {
  final String accountNumber;
  final int? leverage;
  const ChangeLeverageScreen({
    super.key,
    required this.accountNumber,
    this.leverage,
  });

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (createContext) => diContainer<ChangeLeverageBloc>(param1: leverage)
            ..add(
              ChangeLeverageEvent.onGetLeverages(accountNumber: accountNumber),
            ),
      child: BlocConsumer<ChangeLeverageBloc, ChangeLeverageState>(
        buildWhen:
            (previous, current) =>
                previous.changeLeverage != current.changeLeverage,
        listenWhen:
            (previous, current) =>
                previous.changeLeverage != current.changeLeverage,
        listener: (listenerContext, state) {
          if (state.changeLeverage is ChangeLeverageSuccess) {
            Navigator.of(listenerContext).pop(true);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: DuploText(
                  text: localization.trader_updated_leverage_successfully,
                  style: context.duploTextStyles.textSm,
                  fontWeight: DuploFontWeight.medium,
                  color: context.duploTheme.foreground.fgWhite,
                ),
                behavior: SnackBarBehavior.floating,
                showCloseIcon: true,
                duration: const Duration(seconds: 2),
              ),
            );
          }
          if (state.changeLeverage is ChangeLeverageError) {
            final toast = DuploToast();
            final errorState = state.changeLeverage as ChangeLeverageError;
            toast.showToastMessage(
              context: listenerContext,
              widget: DuploToastMessage(
                titleMessage:
                    errorState.errorType ==
                            ChangeLeverageErrorType.changeLeverageBlocked
                        ? localization.trader_change_leverage_blocked_title
                        : localization.trader_somethingWentWrong,
                descriptionMessage:
                    errorState.errorType ==
                            ChangeLeverageErrorType.changeLeverageBlocked
                        ? localization.trader_change_leverage_blocked
                        : localization.trader_unable_to_change_leverage,

                messageType: ToastMessageType.error,
                onLeadingAction: () {
                  toast.hidesToastMessage();
                },
              ),
            );
          }
        },
        builder: (builder1Context, state1) {
          return BlocBuilder<ChangeLeverageBloc, ChangeLeverageState>(
            buildWhen:
                (previous, current) =>
                    previous.processState != current.processState ||
                    previous.changeLeverage != current.changeLeverage,
            builder: (builderContext, state) {
              return switch (state.processState) {
                ChangeLeverageLoading() || ChangeLeverageInitial() => Center(
                  child: CircularProgressIndicator(),
                ),
                ChangeLeverageSuccess() || ChangeLeverageError() => () {
                  final allOptions = <SelectionOptionModel>[];

                  state.leverages.forEach((element) {
                    final model = SelectionOptionModel(
                      displayText:
                          "1:${EquitiFormatter.formatNumber(value: element, locale: Localizations.localeOf(context).toString())}",
                      identifier: EquitiFormatter.formatNumber(
                        value: element,
                        locale: Localizations.localeOf(context).toString(),
                      ),
                    );

                    allOptions.add(model);
                  });

                  // Only show TextSelectionComponentScreen if we have options
                  if (allOptions.isEmpty) {
                    return Center(child: Text("No leverages available"));
                  }

                  return Container(
                    decoration: BoxDecoration(
                      color: context.duploTheme.background.bgSecondary,
                      border: Border(
                        top: BorderSide(
                          color: context.duploTheme.border.borderSecondary,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 10),
                        Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 10,
                          ),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: context.duploTheme.utility.utilityError50,
                            border: Border.all(
                              color: context.duploTheme.utility.utilityError200,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color:
                                    context.duploTheme.utility.utilityError600,
                                size: 20,
                              ),
                              const SizedBox(width: 16),
                              Column(
                                children: [
                                  DuploText(
                                    text: "Important Notice",
                                    style: context.duploTextStyles.textSm,
                                    fontWeight: DuploFontWeight.semiBold,
                                    color:
                                        context.duploTheme.text.textSecondary,
                                  ),
                                  DuploText(
                                    text:
                                        "For your protection, leverage can only be reduced when no positions are open. To lower leverage, please close your positions first.",
                                    style: context.duploTextStyles.textSm,
                                    fontWeight: DuploFontWeight.semiBold,

                                    maxLines: 10,
                                    color:
                                        context.duploTheme.text.textSecondary,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: TextSelectionComponentScreen(
                            pageTitle: "",
                            isLoading:
                                state.changeLeverage is ChangeLeverageLoading,
                            buttonTitle:
                                EquitiLocalization.of(context).trader_save,
                            options: allOptions,
                            selected: allOptions.firstOrNullWhere(
                              (option) =>
                                  int.tryParse(option.identifier) ==
                                  state.selectedLeverage,
                            ),
                            onSelection: (option) {
                              final loc = Localizations.localeOf(
                                builderContext,
                              );
                              builderContext.read<ChangeLeverageBloc>().add(
                                ChangeLeverageEvent.onChangeLeverages(
                                  selectedLeverage:
                                      EquitiFormatter.formatNumber(
                                        value:
                                            int.tryParse(option.identifier) ??
                                            0,
                                        locale: loc.toString(),
                                      ),
                                  accountNumber: accountNumber,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                }(),
              };
            },
          );
        },
      ),
    );
  }
}
